# Generated by Django 5.2.3 on 2025-08-04 09:52

import api.models
import django.db.models.deletion
import django.utils.timezone
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("auth", "0012_alter_user_first_name_max_length"),
    ]

    operations = [
        migrations.CreateModel(
            name="Inquiry",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        blank=True, max_length=200, null=True, verbose_name="Full Name"
                    ),
                ),
                (
                    "email",
                    models.EmailField(max_length=254, verbose_name="Email Address"),
                ),
                (
                    "inquiry_type",
                    models.PositiveSmallIntegerField(
                        choices=[
                            (0, "General Inquiry"),
                            (1, "Technical Support"),
                            (2, "Partnership"),
                            (3, "Media Inquiry"),
                            (4, "Other"),
                        ],
                        default=0,
                        verbose_name="Inquiry Type",
                    ),
                ),
                (
                    "description",
                    models.TextField(max_length=1500, verbose_name="Message"),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("new", "New"),
                            ("in_progress", "In Progress"),
                            ("replied", "Replied"),
                            ("closed", "Closed"),
                        ],
                        default="new",
                        max_length=20,
                        verbose_name="Status",
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
            ],
            options={
                "verbose_name": "Contact Inquiry",
                "verbose_name_plural": "Contact Inquiries",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="CustomUser",
            fields=[
                ("password", models.CharField(max_length=128, verbose_name="password")),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "email",
                    models.EmailField(
                        max_length=254, unique=True, verbose_name="Email Address"
                    ),
                ),
                (
                    "first_name",
                    models.CharField(
                        blank=True, max_length=30, verbose_name="First Name"
                    ),
                ),
                (
                    "last_name",
                    models.CharField(
                        blank=True, max_length=30, verbose_name="Last Name"
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(default=False, verbose_name="Is Active"),
                ),
                (
                    "is_staff",
                    models.BooleanField(default=False, verbose_name="Is Staff"),
                ),
                (
                    "is_superuser",
                    models.BooleanField(default=False, verbose_name="Is Superuser"),
                ),
                (
                    "date_joined",
                    models.DateTimeField(
                        default=django.utils.timezone.now, verbose_name="Date Joined"
                    ),
                ),
                (
                    "last_login",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="Last Login"
                    ),
                ),
                (
                    "avatar",
                    models.ImageField(
                        blank=True,
                        null=True,
                        upload_to="avatars/",
                        verbose_name="Avatar",
                    ),
                ),
                (
                    "has_agreed_to_terms",
                    models.BooleanField(
                        default=False, verbose_name="Has Agreed to Terms"
                    ),
                ),
                (
                    "terms_agreed_at",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="Terms Agreed At"
                    ),
                ),
                (
                    "groups",
                    models.ManyToManyField(
                        blank=True,
                        help_text="The groups this user belongs to. A user will get all permissions granted to each of their groups.",
                        related_name="user_set",
                        related_query_name="user",
                        to="auth.group",
                        verbose_name="groups",
                    ),
                ),
                (
                    "user_permissions",
                    models.ManyToManyField(
                        blank=True,
                        help_text="Specific permissions for this user.",
                        related_name="user_set",
                        related_query_name="user",
                        to="auth.permission",
                        verbose_name="user permissions",
                    ),
                ),
            ],
            options={
                "verbose_name": "User",
                "verbose_name_plural": "Users",
                "db_table": "api_customuser",
            },
        ),
        migrations.CreateModel(
            name="AudioAnalysis",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("filename", models.CharField(max_length=255, verbose_name="Filename")),
                (
                    "audio_file",
                    models.FileField(
                        upload_to=api.models.user_upload_path,
                        verbose_name="Audio File Path",
                    ),
                ),
                (
                    "result",
                    models.TextField(
                        blank=True, null=True, verbose_name="Analysis Result"
                    ),
                ),
                (
                    "upload_time",
                    models.DateTimeField(auto_now_add=True, verbose_name="Upload Time"),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("processing", "Processing"),
                            ("completed", "Completed"),
                            ("failed", "Failed"),
                        ],
                        default="processing",
                        max_length=20,
                        verbose_name="Analysis Status",
                    ),
                ),
                (
                    "relationship",
                    models.CharField(
                        blank=True,
                        help_text="Relationship to speaker (e.g., Myself, My Father, My Mother)",
                        max_length=50,
                        null=True,
                        verbose_name="Audio Speaker",
                    ),
                ),
                (
                    "occupation",
                    models.CharField(
                        blank=True,
                        help_text="Speaker's occupation",
                        max_length=100,
                        null=True,
                        verbose_name="Occupation",
                    ),
                ),
                (
                    "age",
                    models.PositiveIntegerField(
                        blank=True,
                        help_text="Speaker's age",
                        null=True,
                        verbose_name="Age",
                    ),
                ),
                (
                    "date_of_birth",
                    models.CharField(
                        blank=True,
                        help_text="Speaker's date of birth (DD-MM-YYYY format)",
                        max_length=20,
                        null=True,
                        verbose_name="Date of Birth",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="audio_analyses",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="User",
                    ),
                ),
            ],
            options={
                "verbose_name": "Audio Analysis",
                "verbose_name_plural": "Audio Analyses",
                "ordering": ["-upload_time"],
            },
        ),
        migrations.CreateModel(
            name="Donation",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "amount",
                    models.PositiveIntegerField(
                        default=150, verbose_name="Donation Amount (USD)"
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("completed", "Completed"),
                            ("failed", "Failed"),
                            ("refunded", "Refunded"),
                        ],
                        default="pending",
                        max_length=20,
                        verbose_name="Donation Status",
                    ),
                ),
                (
                    "stripe_payment_intent_id",
                    models.CharField(
                        blank=True,
                        max_length=255,
                        null=True,
                        verbose_name="Stripe Payment Intent ID",
                    ),
                ),
                (
                    "stripe_charge_id",
                    models.CharField(
                        blank=True,
                        max_length=255,
                        null=True,
                        verbose_name="Stripe Charge ID",
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "completed_at",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="Completed At"
                    ),
                ),
                (
                    "audio_analysis",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="donations",
                        to="api.audioanalysis",
                        verbose_name="Audio Analysis",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="donations",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="User",
                    ),
                ),
            ],
            options={
                "verbose_name": "Donation",
                "verbose_name_plural": "Donations",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="EmailVerificationToken",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("token", models.CharField(max_length=255, unique=True)),
                (
                    "verification_code",
                    models.CharField(
                        blank=True,
                        max_length=6,
                        null=True,
                        verbose_name="Verification Code",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("expires_at", models.DateTimeField()),
                ("is_used", models.BooleanField(default=False)),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="verification_tokens",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Email Verification Token",
                "verbose_name_plural": "Email Verification Tokens",
            },
        ),
        migrations.CreateModel(
            name="InquiryReply",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "message",
                    models.TextField(max_length=2000, verbose_name="Reply Message"),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "admin_user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Admin User",
                    ),
                ),
                (
                    "inquiry",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="replies",
                        to="api.inquiry",
                    ),
                ),
            ],
            options={
                "verbose_name": "Inquiry Reply",
                "verbose_name_plural": "Inquiry Replies",
                "ordering": ["created_at"],
            },
        ),
        migrations.CreateModel(
            name="MessageBoardMessage",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("is_anonymous", models.BooleanField(default=True)),
                (
                    "anonymous_name",
                    models.CharField(blank=True, default="Anonymous", max_length=100),
                ),
                ("anonymous_email", models.EmailField(blank=True, max_length=254)),
                ("title", models.CharField(max_length=200)),
                ("content", models.TextField()),
                (
                    "image",
                    models.ImageField(
                        blank=True, null=True, upload_to="message_board/images/"
                    ),
                ),
                ("created_at", models.DateTimeField(default=django.utils.timezone.now)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("is_active", models.BooleanField(default=True)),
                (
                    "user",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Message Board Message",
                "verbose_name_plural": "Message Board Messages",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="MessageBoardReply",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("content", models.TextField()),
                (
                    "image",
                    models.ImageField(
                        blank=True, null=True, upload_to="message_board/replies/"
                    ),
                ),
                ("created_at", models.DateTimeField(default=django.utils.timezone.now)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("is_active", models.BooleanField(default=True)),
                (
                    "message",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="replies",
                        to="api.messageboardmessage",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Message Board Reply",
                "verbose_name_plural": "Message Board Replies",
                "ordering": ["created_at"],
            },
        ),
        migrations.CreateModel(
            name="Notification",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "notification_type",
                    models.CharField(
                        choices=[
                            ("message_reply", "Message Reply"),
                            ("audio_complete", "Audio Analysis Complete"),
                            ("audio_failed", "Audio Analysis Failed"),
                            ("system", "System Notification"),
                        ],
                        max_length=20,
                    ),
                ),
                ("title", models.CharField(max_length=200)),
                ("message", models.TextField()),
                ("is_read", models.BooleanField(default=False)),
                ("action_url", models.URLField(blank=True, null=True)),
                (
                    "related_object_id",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("read_at", models.DateTimeField(blank=True, null=True)),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="notifications",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Notification",
                "verbose_name_plural": "Notifications",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="PasswordResetToken",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("token", models.CharField(max_length=255, unique=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("expires_at", models.DateTimeField()),
                ("is_used", models.BooleanField(default=False)),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="password_reset_tokens",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "密码重置令牌",
                "verbose_name_plural": "密码重置令牌",
            },
        ),
        migrations.AddIndex(
            model_name="messageboardmessage",
            index=models.Index(
                fields=["-created_at"], name="api_message_created_f9fb0e_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="messageboardmessage",
            index=models.Index(
                fields=["is_active", "-created_at"],
                name="api_message_is_acti_239cf9_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="messageboardreply",
            index=models.Index(
                fields=["message", "created_at"], name="api_message_message_485283_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="messageboardreply",
            index=models.Index(
                fields=["user", "-created_at"], name="api_message_user_id_e11780_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="notification",
            index=models.Index(
                fields=["user", "-created_at"], name="api_notific_user_id_48bbdc_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="notification",
            index=models.Index(
                fields=["user", "is_read"], name="api_notific_user_id_16328d_idx"
            ),
        ),
    ]
